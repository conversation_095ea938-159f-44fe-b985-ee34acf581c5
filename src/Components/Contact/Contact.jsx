// Contact.jsx
import React, { useState, useMemo, useEffect } from 'react';
import emailjs from '@emailjs/browser';
import { motion, AnimatePresence } from 'framer-motion';
import './Contact.css';
import { useInView } from 'react-intersection-observer';
import FullHeightHero from '../Common/FullHeightHero';

const Contact = () => {
  const [form, setForm] = useState({ subject: '', email: '', message: '', addToEmailList: true });
  const [statusMessage, setStatusMessage] = useState('');
  const [statusType, setStatusType] = useState('');
  const [loading, setLoading] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [focusedField, setFocusedField] = useState(null);
  const [formRef, formInView] = useInView({ threshold: 0.1, triggerOnce: true });

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
  };

  const handleFocus = (field) => setFocusedField(field);
  const handleBlur = () => setFocusedField(null);

  const validateForm = () => {
    const newErrors = {};
    if (!form.subject?.trim()) newErrors.subject = 'Subject is required';
    if (!form.email?.trim()) newErrors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(form.email)) newErrors.email = 'Email is invalid';
    if (!form.message?.trim()) newErrors.message = 'Message is required';
    return Object.keys(newErrors).length === 0;
  };

  const statusMessageElement = useMemo(() => {
    if (statusMessage?.trim()) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.4 }}
          className={`status-message ${statusType}`}
        >
          <div className="status-icon">
            <i className={`bi ${statusType === 'success' ? 'bi-check-circle-fill' : 'bi-exclamation-circle-fill'}`}></i>
          </div>
          <div className="status-text">{statusMessage}</div>
        </motion.div>
      );
    }
    return null;
  }, [statusMessage, statusType]);

  const sendEmail = async (e) => {
    e.preventDefault();
    if (loading) return;

    if (!validateForm()) {
      setStatusMessage('Please fill in all required fields correctly.');
      setStatusType('error');
      return;
    }

    setLoading(true);
    setStatusMessage('');
    setStatusType('');

    try {
      form.subject = 'moolah:capital:contact - '+form.subject;
      await emailjs.send('service_sm28k8v', 'template_3z6s8zt', form, { publicKey: '8sgwUMQOfLINQUnrQ' });
      setStatusMessage('Your message has been sent successfully!');
      setStatusType('success');
      setForm({ subject: '', email: '', message: '', addToEmailList: true });
      setFormSubmitted(true);
      setTimeout(() => setFormSubmitted(false), 5000);
    } catch (err) {
      setStatusMessage('There was an error sending your message. Please try again later.');
      setStatusType('error');
      console.error('Email send error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const canvas = document.getElementById('particle-canvas');
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    canvas.width = window.innerWidth;
    canvas.height = 300;

    const particles = Array.from({ length: 50 }, () => ({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      radius: Math.random() * 2 + 1,
      color: `rgba(255,255,255,${Math.random() * 0.2 + 0.1})`,
      speedX: Math.random() * 0.5 - 0.25,
      speedY: Math.random() * 0.5 - 0.25
    }));

    function animate() {
      requestAnimationFrame(animate);
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      particles.forEach(p => {
        ctx.beginPath();
        ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
        ctx.fillStyle = p.color;
        ctx.fill();
        p.x += p.speedX;
        p.y += p.speedY;
        if (p.x < 0 || p.x > canvas.width) p.speedX *= -1;
        if (p.y < 0 || p.y > canvas.height) p.speedY *= -1;
      });
    }

    animate();
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        canvas.width = window.innerWidth;
        canvas.height = 300;
      }, 100);
    };

    let resizeTimeout;
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="contact-container">
      <FullHeightHero
        backgroundImage="/contact-background.jpg"
        posterImage="/loading-contact.jpg"
        overlayColor="rgba(0, 0, 0, 0.6)"
      >
        <div className="container text-center">
          <h1 className="display-4 text-white mb-4">Contact Us</h1>
          <p className="lead text-white mb-5">We'd love to hear from you</p>
        </div>
      </FullHeightHero>

      <div className="page-content-container" style={{ backgroundColor: '#ffffff' }}>
        <motion.div
        ref={formRef}
        initial={{ opacity: 0, y: 50 }}
        animate={formInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.6, delay: 0.3 }}
        className="contact-form-card mt-0"
      >
        <div className="card-header">
          <div className="card-icon"><i className="bi bi-chat-square-text-fill"></i></div>
          <h2>Send Us a Message</h2>
          <p>Fill out the form below and we&apos;ll get back to you as soon as possible.</p>
        </div>

        <form className="contact-form" onSubmit={sendEmail} noValidate>
          <input type="text" name="honeypot" style={{ display: 'none' }} tabIndex="-1" autoComplete="off" />

          {["subject", "email", "message"].map(field => (
            <div key={field} className={`form-group ${focusedField === field ? 'focused' : ''}`}>
              <div className="input-icon">
                <i className={`bi ${field === 'subject' ? 'bi-tag-fill' : field === 'email' ? 'bi-envelope-fill' : 'bi-chat-left-text-fill'}`}></i>
              </div>
              <div className="input-container">
                {field === 'message' ? (
                  <textarea
                    id={field}
                    name={field}
                    value={form[field]}
                    onChange={handleChange}
                    onFocus={() => handleFocus(field)}
                    onBlur={handleBlur}
                    required
                    rows={4}
                    className="form-input textarea"
                  />
                ) : (
                  <input
                    type={field === 'email' ? 'email' : 'text'}
                    id={field}
                    name={field}
                    value={form[field]}
                    onChange={handleChange}
                    onFocus={() => handleFocus(field)}
                    onBlur={handleBlur}
                    required
                    className="form-input"
                  />
                )}
                <label htmlFor={field} className={form[field] ? 'active' : ''}>
                  {field.charAt(0).toUpperCase() + field.slice(1)} <span className="required">*</span>
                </label>
                <div className="input-highlight"></div>
              </div>
            </div>
          ))}

          {/* 'Add to Email list' checkbox - completely new implementation */}
          <div className="form-group checkbox-group">
            <div className="simple-checkbox-container">
              <div className="checkbox-center-wrapper">
                <input
                  type="checkbox"
                  id="addToEmailList"
                  name="addToEmailList"
                  checked={form.addToEmailList}
                  onChange={(e) => {
                    setForm(prev => ({ ...prev, addToEmailList: e.target.checked }));
                  }}
                  className="simple-checkbox-input"
                />
                <label htmlFor="addToEmailList" className="simple-checkbox-label text-start">
                  Add me to Moolah Capital email list
                </label>
              </div>
            </div>
          </div>

          <div className="form-group button-group">
            <motion.button
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              className={`submit-button sec-4-button ${formSubmitted ? 'submitted' : ''}`}
              type="submit"
              disabled={loading}
            >
              <span className="button-content">
                {loading ? (
                  <><span className="spinner"></span><span className="button-text">Sending...</span></>
                ) : formSubmitted ? (
                  <><i className="bi bi-check-circle-fill"></i><span className="button-text">Sent!</span></>
                ) : (
                  <><i className="bi bi-send-fill"></i><span className="button-text">Send Message</span></>
                )}
              </span>
            </motion.button>
          </div>

          <AnimatePresence>{statusMessageElement}</AnimatePresence>
        </form>
      </motion.div>
      </div>
    </div>
  );
};
export default Contact;
