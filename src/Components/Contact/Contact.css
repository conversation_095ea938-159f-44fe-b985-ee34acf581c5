/* === CSS Variables === */
:root {
  --primary: #8B1E2D;
  --primary-color: #8B1E2D;
  --primary-hover: #0d6efd;
}

/* === Container === */
.contact-container {
  /* Remove custom padding to use standard layout */
}

/* === Contact Form Section === */
.contact-form-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 600px;
  margin: 0 auto;
  box-shadow: 0 8px 24px rgba(104, 1, 7, 0.25);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-form-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 28px rgba(104, 1, 7, 0.35);
}

.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.card-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--primary);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
}

.input-container {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
  outline: none;
  transition: border 0.3s ease;
}

.form-input:focus {
  border-color: var(--primary);
}

label {
  left: 40px;
  top: 12px;
  color: #999;
  font-size: 0.9rem;
  pointer-events: none;
  transition: all 0.2s ease;
  font-family: var(--font-body);
}

label.active,
.form-input:focus + label {
  top: -10px;
  font-size: 0.75rem;
  color: var(--primary);
}

.input-highlight {
  height: 2px;
  background: var(--primary);
  width: 0;
  transition: width 0.3s ease;
}

.form-input:focus ~ .input-highlight {
  width: 100%;
}

/* Simple checkbox styling - completely new implementation */
.simple-checkbox-container {
  display: flex;
  align-items: center;
  justify-content: center; /* Center the container */
  margin: 10px 0 15px;
  width: 100%; /* Ensure container takes full width */
}

.checkbox-center-wrapper {
  display: inline-flex;
  align-items: right;
  gap: 5px; /* Space between checkbox and label */
}

/* Adjust button group spacing */
.form-group.button-group {
  margin-top: 10px; /* Reduce from default spacing */
}

/* === Status Message === */
.status-message {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  padding: 0.8rem 1rem;
  border-radius: 6px;
  font-size: 0.95rem;
}

.status-message.success {
  background: #e6f9ec;
  color: #2e7d32;
}

.status-message.error {
  background: #fdecea;
  color: #c62828;
}

/* === Submit Button === */
.submit-button {
  background: var(--primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: var(--font-body);
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(139, 30, 45, 0.2);
}

.submit-button:hover {
  background: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 6px 18px rgba(13, 110, 253, 0.4);
}

.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1rem;
  width: 100%;
}

.button-text {
  display: inline-block;
  text-align: center;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #fff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* === Page Content Container === */
.page-content-container {
  margin-top: 0;
  position: relative;
  z-index: 2;
  background-color: #fff;
  padding: var(--body-content-py-margin-dekstop) var(--body-content-px-margin-dekstop);
}

@media (max-width: 768px) {
  .page-content-container {
    padding: var(--body-content-py-margin-mobile) var(--body-content-px-margin-mobile);
  }
}

/* === Responsive Styles === */
@media (max-width: 768px) {
  .contact-form-card {
    padding: 1.5rem;
    margin: 0 1rem;
  }

  .form-input {
    font-size: 0.95rem;
  }

  .submit-button {
    padding: 0.65rem 1.2rem;
    font-size: 0.95rem;
  }

  .card-icon {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .contact-form-card {
    padding: 1rem;
    margin: 0 0.5rem;
  }

  .simple-checkbox-label {
    font-size: 0.75rem;
  }
}

/* === Additional Styling Enhancements === */

.card-icon {
  color: var(--primary);
}

.card-header h2 {
  font-family: var(--font-header);
  color: #000000;
  margin-bottom: 0.5rem;
}

.card-header p {
  font-family: var(--font-body);
  color: #666;
  margin-bottom: 0;
}

label.active,
.form-input:focus + label {
  color: var(--primary);
  background-color: white;
  padding: 0 5px;
}

.form-group label {
  color: #555;
  font-family: var(--font-body);
}

.form-input {
  font-family: var(--font-body);
}

.button-group {
  margin-top: 20px;
}

.simple-checkbox-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0 15px;
  width: 100%;
}

.checkbox-center-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.simple-checkbox-input {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary);
}

.simple-checkbox-label {
  font-size: 0.80rem;
  color: #333;
  cursor: pointer;
  font-family: var(--font-body);
}

