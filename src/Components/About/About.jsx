import React, { useState, useEffect, useMemo } from 'react';
import useAssetCache from '../../context/useAssetCache.js';
import CTASection from '../../App/CTA/CTASection.jsx';
import useFadeInOnScroll from '../../hooks/useFadeInOnScroll';
import useRefsArray from '../../hooks/useRefsArray';
import useVisibilityObserver from '../../hooks/useVisibilityObserver';
import FullHeightHero from '../Common/FullHeightHero';

// Global color variables for About page
const ABOUT_COLORS = {
  DARK_BLUE: '#0D1B2E',
  MEDIUM_BLUE: '#0F2037',
  WHITE: '#ffffff'
};

const About = () => {
  const imgAbout = useAssetCache('imgAbout', '/about-background.png');
  const [ref2, isVisible2] = useFadeInOnScroll();
  const [ref3, isVisible3] = useFadeInOnScroll();
  const [ref4, isVisible4] = useFadeInOnScroll();

  // Make sure all history items have a valid theme
  const historyList = useMemo(() => [
    { theme: "launch", number: 1, title: "In the beginning", content: "Moolah Capital was founded by two finance sector veterans who combined their expertise in business, technology, and analytics to tackle the challenges of crypto investing. With backgrounds in traditional finance, including stints at Barclays Investment Bank, Bank of America, and Lloyds Bank, they were well-equipped to analyze the crypto market." },
    { theme: "problem", number: 2, title: "Identifying The Problem", content: "The team recognized that crypto investing was fraught with risks, including volatile markets, untrustworthy advisors, and confusing marketing. The steep learning curve required to keep up with new coins and protocols made it difficult for most people to invest in crypto." },
    { theme: "team", number: 3, title: "A New Approach", content: "Moolah team applied standard techniques from equity, forex, and rates markets to investigate the crypto market. They analyzed multiple coins, verifying their characteristics, such as volatility, liquidity, correlation to other assets, and historical duration. This fundamental analysis led to the selection of a candidate set of coins that generated stable returns with a good risk trade-off." },
    { theme: "technology", number: 4, title: "Developing Trading Signals", content: "Moolah team applied standard techniques from equity, forex, and rates markets to investigate the crypto market. They analyzed multiple coins, verifying their characteristics, such as volatility, liquidity, correlation to other assets, and historical duration. This fundamental analysis led to the selection of a candidate set of coins that generated stable returns with a good risk trade-off." },
    { theme: "learning", number: 5, title: "Continuous Learning", content: "Moolah team applied standard techniques from equity, forex, and rates markets to investigate the crypto market. They analyzed multiple coins, verifying their characteristics, such as volatility, liquidity, correlation to other assets, and historical duration. This fundamental analysis led to the selection of a candidate set of coins that generated stable returns with a good risk trade-off." },
    { theme: "solution", number: 6, title: "The Future of Crypto Investing", content: "Moolah Capital plans to offer a core set of funds under three thematic approaches: Passive, Smart Beta, and Special Situations. The team continues to search for new fund ideas, evaluating them using the same rigorous methodology. Follow Moolah Capital's News page and social media channels for updates on their journey to revolutionize crypto investing." },
  ], []);

  // Let's log the themes to debug
  useEffect(() => {
    console.log("History themes:", historyList.map(item => item.theme));
  }, [historyList]);

  const convictionList = [
    {
      id: 1,
      title: "Access to all Markets",
      img: '/moolah-about-access-markets.jpg',
      description: "Our strategies span the full spectrum of the digital asset landscape - from blue-chip cryptocurrencies to emerging DeFi protocols—providing unparalleled access to global crypto innovation across crypto exchanges, lending platforms, NFTs, and more."
    },
    {
      id: 2,
      title: "Diversification lowers risk",
      img: '/moolah-about-diversification.jpg',
      description: "We build diversified portfolios that mirror the characteristics of index funds while applying smart filters for liquidity, volatility, and correlation. This reduces exposure to any single asset and enhances long-term stability across market cycles."
    },
    {
      id: 3,
      title: "Disruption creates opportunities",
      img: '/moolah-about-opportunities.jpg',
      description: "We identify and invest early in disruptive crypto technologies—such as L2 scaling, cross-chain interoperability, and tokenized real-world assets—that have the potential to reshape traditional finance and generate outsized returns."
    },
    {
      id: 4,
      title: "Growth in adoption and usage",
      img: '/img-08.jpg',
      description: "We track user growth, transaction volume, and developer activity to allocate capital toward protocols and platforms gaining traction. Increased adoption drives demand—and in turn, long-term price appreciation—for high-utility tokens."
    },
    {
      id: 5,
      title: "Active management & learning",
      img: '/img-07.jpg',
      description: "Our adaptive strategies use data-driven insights to navigate market volatility, rebalance exposure, and learn continuously from evolving macro and on-chain signals. We actively optimize positions to stay ahead of shifting dynamics."
    },
    {
      id: 6,
      title: "Decentralized long-term rewards",
      img: '/img-09.jpg',
      description: "We invest in protocols that incentivize long-term participation through staking, governance, and token rewards. These decentralized models offer resilient income streams and align well with long-horizon investment objectives."
    }
  ];


  const convictionRefs = useRefsArray(convictionList.length);
  const [convictionVisible, setConvictionVisible] = useState(convictionList.map(() => false));
  useVisibilityObserver(convictionRefs, setConvictionVisible);

  return (
    <div className="text-black">
      {/* Hero Section - Full Viewport Height */}
      <FullHeightHero
        backgroundImage={imgAbout?.src}
        posterImage="/loading-about.jpg"
        overlayColor="rgba(0, 0, 0, 0.4)"
      >
        <div className="container text-center responsive-text-container">
          <h1 className="display-6 text-white mb-4 no-break-title">
            <span className="title-text">About</span> <span className="brand-name" style={{ color: 'white !important' }}>Moolah Capital</span>
          </h1>
          <p className="lead text-white mb-5">Our story, mission, and values</p>
        </div>
      </FullHeightHero>

      {/* Content Container - Starts after full viewport */}
      <div className="page-content-container">

        <section id="about-history" className="journey-history-section" style={{ backgroundColor: ABOUT_COLORS.WHITE }}>
          <div className="position-relative">
            {/* Background decorative elements - Hidden to fix display issues */}
            {/* <div className="journey-background-elements">
              <div className="journey-element journey-start">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polygon points="16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76"></polygon>
                </svg>
              </div>
              <div className="journey-element journey-milestone">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                  <line x1="4" y1="22" x2="4" y2="15"></line>
                </svg>
              </div>
              <div className="journey-element journey-growth">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
                  <polyline points="17 6 23 6 23 12"></polyline>
                </svg>
              </div>
              <div className="journey-element journey-achievement">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="8" r="7"></circle>
                  <polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline>
                </svg>
              </div>
              <div className="journey-element journey-future">
                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
              </div>
            </div> */}
            <div ref={ref2} className={`fade-in-section ${isVisible2 ? 'is-visible' : ''}`}>
              <div className="body-content-py-margin-compact w-100 overflow-hidden sec-top body-content-px-margin">
                <div className="container text-black">
                  <p></p>
                  <h2 className="h2 text-center mb-4">Our Journey</h2>
                  <p className='body-text text-center mb-5' style={{ color: 'black' }}>The story of how we built Moolah Capital from vision to reality</p>
                  <div className='row g-4'>
                    {historyList.map((item, idx) => (
                      <div key={idx} className='col-md-4 mb-4'>
                        <div className='card h-100' style={{
                          borderRadius: '16px',
                          padding: '24px',
                          backgroundColor: 'var(--primary, #8B1E2D)',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'space-between',
                          height: '100%',
                          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15)',
                          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                          border: 'none',
                          color: 'white'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-4px)';
                          e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.2)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.15)';
                        }}>
                          <div className='card-body d-flex flex-column justify-content-between text-center'>
                            <div>
                              <h3 className='h4 mb-3 fw-bold' style={{ color: 'white' }}>{item.number}. {item.title}</h3>
                              <p className='card-text small' style={{ color: 'white' }}>{item.content}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section id="about-cta" style={{ backgroundColor: ABOUT_COLORS.MEDIUM_BLUE, padding: '60px 20px' }}>
          <div className="container text-center-mbl text-white">
            <div ref={ref3} className={`fade-in-section ${isVisible3 ? 'is-visible' : ''}`}>
              <CTASection
                cta="Click Here to view Funds"
                title="Explore the Funds Matching Your Goals"
                link="/funds"
                theme='learn'
                titleStyle={{ color: 'white' }}
                containerStyle={{
                  backgroundColor: ABOUT_COLORS.DARK_BLUE,
                  border: 'none'
                }}
              />
            </div>
          </div>
        </section>

        {/* Anchoring Convictions Header */}
        <section style={{ backgroundColor: ABOUT_COLORS.DARK_BLUE, padding: '60px 20px 30px 20px' }}>
          <div className="container text-white">
            <div ref={ref4} className={`fade-in-section ${isVisible4 ? 'is-visible' : ''}`}>
              <h2 className="h2 text-center mb-4">Anchoring Convictions</h2>
              <p className='body-text text-center mb-0'>The core beliefs that guide our investment philosophy</p>
            </div>
          </div>
        </section>

        {/* Alternating Conviction Sections */}
        {convictionList.map((service, index) => (
          <section
            key={service.id}
            style={{
              backgroundColor: index % 2 === 0 ? ABOUT_COLORS.MEDIUM_BLUE : ABOUT_COLORS.DARK_BLUE,
              minHeight: '500px',
              padding: '60px 20px',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            <div className="container text-white">
              <div
                ref={convictionRefs[index]}
                className={`row align-items-center fade-in-section ${convictionVisible[index] ? 'is-visible' : ''} ${index % 2 !== 0 ? 'flex-column flex-md-row' : 'flex-md-row-reverse flex-column'}`}
              >
                <div className="col-md-6 my-3 d-flex align-items-center justify-content-center">
                  <div className="w-100 px-4">
                    <h3 className="h2 text-start mb-4 fw-bold" style={{ color: 'white' }}>
                      {service.title}
                    </h3>
                    <p className="body-text text-start" style={{ color: 'white' }}>{service.description}</p>
                  </div>
                </div>
                <div className="col-md-6 my-3 d-flex align-items-center justify-content-center">
                  <div className="w-100 px-4">
                    <img
                      src={service.img}
                      alt={service.title}
                      className="img-fluid rounded"
                      style={{ maxHeight: '400px', width: '100%', objectFit: 'cover' }}
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = "/placeholder.jpg";
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </section>
        ))}

        <section id="funds-cta1" style={{ backgroundColor: ABOUT_COLORS.MEDIUM_BLUE, padding: '60px 20px' }}>
          <div className="container text-center-mbl text-white">
            <CTASection
              title="Start Your Financial Journey"
              cta="Sign up"
              link="/signup#account-signup"
              theme="signup"
              titleStyle={{ color: 'white' }}
              containerStyle={{
                backgroundColor: ABOUT_COLORS.DARK_BLUE,
                border: 'none'
              }}
            />
          </div>
        </section>
      </div>
    </div>
  );
};
export default About;
