import React from 'react';

const FundChart = ({ fundData, compact = false, textColor = "text-white", backgroundColor = "transparent" }) => {
  const getColorStyle = (color) => {
    const colorMap = {
      'bg-green-500': '#10b981',
      'bg-red-500': '#8B1E2D', 
      'bg-blue-500': '#3b82f6',
      'bg-purple-500': '#8b5cf6'
    };
    return colorMap[color] || '#3b82f6';
  };

  const ProgressBar = ({ label, value, color = "bg-blue-500" }) => (
    <div className={compact ? "mb-2" : "mb-3"}>
      <div className="d-flex justify-content-between mb-1">
        <span className={`${compact ? 'small' : 'small'} fw-medium ${textColor}`}>{label}</span>
        <span className={`${compact ? 'small' : 'small'} ${textColor === 'text-white' ? 'text-white-50' : 'text-muted'}`}>{value}%</span>
      </div>
      <div className="progress" style={{ height: compact ? '6px' : '8px' }}>
        <div 
          className="progress-bar"
          role="progressbar"
          style={{ 
            width: `${value}%`,
            backgroundColor: getColorStyle(color),
            transition: 'width 0.5s ease'
          }}
          aria-valuenow={value}
          aria-valuemin="0"
          aria-valuemax="100"
        ></div>
      </div>
    </div>
  );

  if (!fundData) return null;

  const finalBackgroundColor = backgroundColor;

  return (
    <div
      className={`${compact ? 'p-3' : 'p-4'} h-100`}
      style={{
        backgroundColor: finalBackgroundColor
      }}
    >
      <h4 className={`${compact ? 'h6' : 'h5'} fw-bold mb-3 ${textColor}`}>{fundData.name}</h4>
      
      <ProgressBar 
        label="Performance" 
        value={fundData.performance} 
        color="bg-green-500" 
      />
      
      <ProgressBar 
        label="Risk Level" 
        value={fundData.risk} 
        color="bg-red-500" 
      />
      
      <ProgressBar 
        label="Low Fees" 
        value={fundData.fees} 
        color="bg-blue-500" 
      />
      
      <ProgressBar 
        label="Liquidity" 
        value={fundData.liquidity} 
        color="bg-purple-500" 
      />
      
      <div className={`${compact ? 'mt-3 pt-2' : 'mt-4 pt-3'} border-top ${textColor === 'text-white' ? 'border-white-50' : 'border-secondary'}`}>
        <div className={`small ${textColor === 'text-white' ? 'text-white-50' : 'text-muted'}`}>
          Overall Score:
          <span className={`fw-bold ms-1 ${textColor}`}>
            {Math.round((fundData.performance + fundData.fees + fundData.liquidity - fundData.risk) / 3)}%
          </span>
        </div>
      </div>
    </div>
  );
};

export default FundChart;
