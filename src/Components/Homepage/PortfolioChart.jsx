import React from 'react';

const PortfolioChart = ({ portfolioData, compact = false, textColor = "text-white", backgroundColor = "transparent" }) => {
  if (!portfolioData || !portfolioData.data) {
    return <div>No portfolio data available</div>;
  }

  const allocations = portfolioData.data;
  const totalValue = Object.values(allocations).reduce((sum, value) => sum + value, 0);

  const AllocationBar = ({ label, value, color = "#3b82f6" }) => {
    const percentage = ((value / totalValue) * 100).toFixed(1);
    
    return (
      <div className={compact ? "mb-2" : "mb-3"}>
        <div className="d-flex justify-content-between mb-1">
          <span className={`${compact ? 'small' : 'small'} fw-medium ${textColor}`} style={{ fontSize: compact ? '0.75rem' : '0.85rem' }}>
            {label}
          </span>
          <span className={`${compact ? 'small' : 'small'} ${textColor === 'text-white' ? 'text-white-50' : 'text-muted'}`}>
            ${value.toLocaleString()}
          </span>
        </div>
        <div className="progress" style={{ height: compact ? '6px' : '8px' }}>
          <div 
            className="progress-bar"
            role="progressbar"
            style={{ 
              width: `${percentage}%`,
              backgroundColor: color,
              transition: 'width 0.5s ease'
            }}
            aria-valuenow={percentage}
            aria-valuemin="0"
            aria-valuemax="100"
          ></div>
        </div>
      </div>
    );
  };

  const colors = ['#10b981', '#8B1E2D', '#3b82f6', '#8b5cf6', '#ef4444'];
  const fundNames = Object.keys(allocations);

  const finalBackgroundColor = backgroundColor;

  return (
    <div
      className={`${compact ? 'p-3' : 'p-4'} h-100`}
      style={{
        backgroundColor: finalBackgroundColor
      }}
    >
      <h4 className={`${compact ? 'h6' : 'h5'} fw-bold mb-3 ${textColor}`}>{portfolioData.name}</h4>
      
      {fundNames.map((fundName, index) => (
        <AllocationBar 
          key={fundName}
          label={fundName} 
          value={allocations[fundName]} 
          color={colors[index % colors.length]} 
        />
      ))}
      
      <div className={`${compact ? 'mt-3 pt-2' : 'mt-4 pt-3'} border-top ${textColor === 'text-white' ? 'border-white-50' : 'border-secondary'}`}>
        <div className={`small ${textColor === 'text-white' ? 'text-white-50' : 'text-muted'}`}>
          Total Portfolio Value:
          <span className={`fw-bold ms-1 ${textColor}`}>
            ${totalValue.toLocaleString()}
          </span>
        </div>
      </div>
    </div>
  );
};

export default PortfolioChart;
