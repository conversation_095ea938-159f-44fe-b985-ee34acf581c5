/* More targeted approach to hide unwanted text while keeping content visible */

/* Only hide text nodes and non-fund container elements */
.page-container > *:not(.fund-container):not(.fund-main-container) {
  display: none !important;
}

/* Allow the fund container to display properly */
.page-container .fund-container,
.page-container .fund-main-container {
  display: block !important;
}

/* Hide text nodes but keep elements */
.page-container:before,
.page-container:after {
  content: none !important;
}

@media (max-width: 768px) {
  /* Ensure the fund container is visible */
  .fund-container,
  .fund-main-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  /* Remove any margins that might be pushing content down */
  .page-container {
    padding-top: 0 !important;
    margin-top: 0 !important;
  }
  
  /* Ensure the tabs are visible */
  .tabs-header,
  .tab-content {
    display: block !important;
    visibility: visible !important;
  }
}
