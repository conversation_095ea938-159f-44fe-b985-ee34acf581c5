@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-heading {
  text-align: left;
  margin-left: 0;
}

/* Page container styles */
.page-container {
  padding: 0;
  margin-top: 60px; /* Add margin to push content below navbar */
}

.fund-subtitle {
  font-size: 1.2rem;
  margin-bottom: 5px;
  opacity: 0.9;
}

.fund-description {
  font-size: 1rem;
  opacity: 0.8;
  max-width: 600px;
  margin: 0;
  text-align: left;
}

/* Overview tab styles */
.overview-container {
  text-align: left;
  padding: 20px 0 30px; /* Increased padding */
}

.fund-container {
  padding: 1rem;
  font-family: var(--font-body);
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  margin-top: 20px; /* Add top margin to push below navbar */
}

/* Fund content styles */
.fund-content {
  width: 100%;
  padding-top: 0; /* Remove top padding */
}

/* Tabs wrapper to ensure proper positioning */
.tabs-wrapper {
  width: 100%;
  margin-top: 20px; /* Add top margin */
  padding-top: 10px; /* Add padding to create space */
}

/* Ensure tabs are visible on all devices */
.tabs-container {
  width: 100%;
  margin-top: 0;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  transition: box-shadow 0.3s ease;
}

.tabs-header {
  margin-top: 0;
  position: relative;
  display: flex;
  justify-content: center;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Responsive styles */
@media (min-width: 769px) {
  /* Styles for laptop and desktop */
  .fund-container {
    padding: 1.5rem; /* Increase padding */
  }
  
  .tabs-wrapper {
    margin-top: 10px; /* Reduce top margin */
  }
  
  .tabs-header {
    justify-content: center; /* Center tabs */
    padding: 10px 0; /* Add vertical padding */
  }
}

@media (max-width: 768px) {
  /* Styles for mobile */
  .fund-container {
    padding: 1rem; /* Maintain padding */
  }
  
  .tabs-wrapper {
    margin-top: 5px; /* Reduce top margin */
  }
  
  .tabs-header {
    justify-content: flex-start; /* Start from left */
    padding: 5px 0; /* Reduce vertical padding */
  }
}

.fund-details p {
  margin: 0.25rem 0;
}

.fund-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
}

.meta-item {
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  background-color: #ffffff;
  min-width: 200px;
  color: #0d0d0d;
  box-shadow: 0 2px 3px rgba(126, 14, 14, 0.3); /* sharper, smaller blur */
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.meta-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 6px rgba(126, 14, 14, 0.5); /* more defined on hover */
}

.fund-description,
.investment-objective,
.key-features,
.fund-performance,
.risk-profile {
  margin-bottom: 25px;
  color: #0d0d0d;
  text-align: left;
}

/* Consistent heading styles for all sections */
.overview-container h3,
.fund-description h3 {
  font-family: var(--font-header);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
  text-align: center;
  justify-content: center;
}

/* Risk meter */
.risk-meter {
  margin: 15px 0;
}

.risk-bar {
  height: 10px;
  background-color: #e0e0e0;
  border-radius: 5px;
  position: relative;
  margin-bottom: 5px;
}

.risk-indicator {
  position: absolute;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  top: -5px;
  transform: translateX(-50%);
  background-color: #ff6384;
}

.risk-indicator.low { left: 16.7%; }
.risk-indicator.medium {
  left: 50%;
  background-color: #ffce56;
}
.risk-indicator.high {
  left: 83.3%;
  background-color: #ea10ee;
}

.risk-labels {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

/* Holdings tab styles */
.holdings-container {
  padding: 5px 0 20px;
  color: black;
}

.holdings-table {
  overflow-x: auto;
}

.charts-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 30px 0;
  width: 100%;
}

.chart-box {
  width: 48%;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.chart-container {
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: auto;
}

.chart-container canvas {
  padding: 1rem 0;
}

/* Performance tab styles */
.performance-container {
  padding: 5px 0 20px;
  color: black;
}

.performance-metrics {
  margin-top: 30px;
}

.chart-note {
  text-align: center;
  font-size: 0.9rem;
  color: #666;
  margin-top: 10px;
}

.positive-return {
  color: #4caf50;
  font-weight: bold;
}

.negative-return {
  color: #f44336;
  font-weight: bold;
}

/* Documents tab styles */
.documents-section {
  padding: 5px 0 20px;
  color: black;
}

.document-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.document-card {
  border: 1px solid #eee;
  border-radius: 12px;
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 150px;
}

.document-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.document-icon {
  font-size: 1.25rem;
  color: #000;
}

.document-title h3 {
  margin: 0;
  font-weight: 600;
  font-size: 1.05rem;
}

.download-button {
  margin-top: 1rem;
  border: 1px solid black;
  border-radius: 999px;
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  background-color: white;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

/* What If tab styles */
.whatif-container {
  padding: 5px 0 20px;
  color: black;
}

.whatif-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 20px 0;
  gap: 20px;
}

.investment-summary {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 30%;
}

.summary-box {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.summary-box h3 {
  margin-top: 0;
  font-size: 1rem;
  color: #0f0c0c;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 10px 0 0;
  color: #0f0c0c;
}

/* Ensure positive and negative return colors override the default summary-value color */
.summary-value.positive-return {
  color: #4caf50;
}

.summary-value.negative-return {
  color: #f44336;
}

.whatif-container .chart-box {
  width: 65%;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Slider styles */
.slider-container {
  margin: 30px 0;
  width: 100%;
  max-width: 600px;
}

.slider-container label {
  display: block;
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 1.1rem;
}

.investment-slider {
  width: 100%;
  height: 8px;
  appearance: none;
  background: #e0e0e0;
  outline: none;
  border-radius: 4px;
}

.investment-slider::-webkit-slider-thumb,
.investment-slider::-moz-range-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4BC0C0;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 0.9rem;
  color: #666;
}

/* Media Queries */
@media (max-width: 768px) {
  .charts-container,
  .whatif-content {
    flex-direction: column;
  }

  .chart-box,
  .whatif-container .chart-box {
    width: 100%;
  }

  .investment-summary {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .summary-box {
    flex: 1;
    min-width: 200px;
  }

  /* Responsive tabs */
  .tabs-header {
    padding-bottom: 5px;
  }

  .tab-button {
    padding: 8px 15px;
    font-size: 14px;
  }

  .fund-container {
    padding: 1rem;
  }

  .fund-content {
    margin-top: 10px;
  }
}

@media (max-width: 600px) {
  .fund-details {
    flex-direction: column;
    gap: 8px;
  }

  .meta-item {
    min-width: 100%;
  }

  .table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}
.centered-chart {
  position: relative;
  height: 250px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Add these new styles for mobile-friendly tab content */
.fund-title {
  text-align: center;
  margin-bottom: 1rem;
}

.tab-content-centered {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Ensure tables are responsive */
.tab-content-centered table {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
  display: block;
}

/* Center align text in tab content on mobile */
@media (max-width: 768px) {
  .overview-container,
  .holdings-container,
  .performance-container,
  .documents-section,
  .whatif-container {
    text-align: center !important;
    padding: 10px !important;
  }
  
  .overview-container h3,
  .holdings-container h3,
  .performance-container h3,
  .documents-section h3,
  .whatif-container h3 {
    text-align: center !important;
    justify-content: center !important;
  }
  
  .fund-meta,
  .meta-item,
  .fund-description,
  .investment-objective,
  .fund-performance,
  .risk-profile {
    text-align: center !important;
    margin-left: 0 !important;
    padding-left: 0 !important;
  }
  
  /* Remove left margins that might be causing alignment issues */
  .ms-4 {
    margin-left: 0 !important;
  }
  
  /* Center charts and tables */
  .charts-container {
    justify-content: center !important;
  }
  
  .chart-box {
    margin: 0 auto 20px;
  }
  
  /* Ensure fund details are centered */
  .fund-details {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  /* Make sure tables are centered but still scrollable */
  .holdings-table,
  .performance-metrics table {
    margin: 0 auto;
    text-align: center;
  }
}

/* Add these styles to hide any unwanted text on mobile */
@media (max-width: 768px) {
  /* Hide any text that might be appearing outside the fund container */
  body > p, 
  #root > p,
  .page-container > p {
    display: none !important;
  }
  
  /* Ensure the fund container starts at the top */
  .fund-container {
    margin-top: 0 !important;
  }
  
  /* Hide any strategy text that might be leaking */
  .fund-strategy-text {
    display: none !important;
  }
}

/* Improve fund header visibility with more vertical margins */
.fund-header {
  border-bottom: 1px solid #ccc;
  margin-bottom: 15px; /* Reduced bottom margin */
  margin-top: 20px;
  padding-top: 15px;
  padding-bottom: 15px;
  color: #0d0d0d;
  text-align: center;
}

/* Ensure the fund title is always visible */
.fund-title {
  font-size: 1.8rem;
  margin-bottom: 5px; /* Reduced bottom margin */
  margin-top: 10px;
  font-weight: bold;
  text-align: center;
}

/* Add specific styling for the overview header */
.fund-overview-title {
  font-family: var(--font-header);
  font-size: 1.4rem;
  font-weight: 600;
  margin-top: 15px; /* Reduced top margin */
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

/* Ensure consistent spacing in the overview container */
.overview-container {
  text-align: left;
  padding: 10px 0 30px; /* Reduced top padding */
}

/* Add responsive adjustments */
@media (max-width: 768px) {
  .fund-header {
    margin-top: 10px; /* Further reduced top margin on mobile */
    margin-bottom: 5px; /* Further reduced bottom margin on mobile */
    padding-top: 10px; /* Reduced padding */
    padding-bottom: 5px; /* Reduced padding */
  }
  
  .fund-overview-title {
    margin-top: 0; /* Remove top margin on mobile */
    margin-bottom: 15px; /* Keep some bottom margin */
  }
  
  /* Ensure tabs have enough vertical space on mobile */
  .tabs-header {
    flex-wrap: wrap;
    justify-content: center;
    padding: 0 5px 5px;
    margin-top: 0; /* Remove top margin */
    margin-bottom: 5px; /* Reduced bottom margin */
  }
  
  /* Ensure tab content has minimal padding */
  .tab-content {
    padding-top: 5px; /* Minimal top padding */
  }
  
  /* Reduce spacing in the overview container on mobile */
  .overview-container {
    padding-top: 0; /* Remove top padding on mobile */
  }
}

/* Ensure tabs are visible on mobile */
@media (max-width: 768px) {
  /* Make sure the tabs container is visible */
  .tabs-container {
    display: block !important;
    visibility: visible !important;
    width: 100%;
    overflow: visible;
  }
  
  /* Ensure the tabs header is visible */
  .tabs-header {
    display: flex !important;
    visibility: visible !important;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 5px;
    margin: 10px 0;
    justify-content: flex-start; /* Start from the left */
  }
  
  /* Make tab buttons more compact */
  .tab-button {
    padding: 8px 12px;
    font-size: 14px;
    margin: 0 2px;
    white-space: nowrap;
    flex-shrink: 0;
  }
  
  /* Ensure the active tab is visible */
  .tab-button.active-tab {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .page-container {
    margin-top: 70px; /* Increase margin to push content below navbar */
  }
  
  .fund-container {
    margin-top: 30px; /* Increase top margin on mobile */
    padding-top: 20px; /* Add padding to create more space */
  }
  
  .tabs-wrapper {
    margin-top: 10px; /* Adjust top margin */
  }
  
  .tabs-header {
    justify-content: flex-start; /* Start from left */
    padding: 5px 0;
    overflow-x: auto; /* Enable horizontal scrolling */
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    white-space: nowrap; /* Prevent wrapping */
    display: flex !important;
    visibility: visible !important;
  }
  
  .tab-button {
    padding: 8px 12px;
    font-size: 14px;
    margin: 0 2px;
    white-space: nowrap;
    flex-shrink: 0; /* Prevent buttons from shrinking */
  }
}

/* More compact fund section spacing */
.fund-section {
  padding-top: 5px !important;
  margin-top: 5px !important;
}

/* Reduce space between fund section title and content */
.fund-section h2 {
  margin-bottom: 10px !important;
}

/* Ensure fund sections have proper scroll margin */
#passive, #smartbeta, #special {
  scroll-margin-top: 80px !important;
  padding-top: 5px !important;
}

/* Reduce space in fund cards */
.funds-card {
  padding: 15px !important;
}

/* Reduce space in fund headers */
.fund-header {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}
