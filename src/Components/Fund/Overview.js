import React from 'react';
import './fund.css';

// Global color variables for Fund Overview
const OVERVIEW_COLORS = {
  DARK_BLUE: '#0D1B2E',
  MEDIUM_BLUE: '#0F2037',
  WHITE: '#ffffff'
};

const Overview = ({ fundDetails = {}, fundName }) => {
  return (
    <div className="overview-container" style={{ paddingTop: 0 }}>
      <div className="text-start mb-4 d-flex align-items-center" style={{ gap: '0.5rem', justifyContent: 'center' }}>
        <div className="line d-none d-md-block" style={{ backgroundColor: OVERVIEW_COLORS.DARK_BLUE, width: '40px', height: '3px' }}></div>
        <h3 className="fund-overview-title section-heading" style={{
          marginTop: window.innerWidth <= 768 ? '10px' : '15px',
          marginBottom: 0,
          color: OVERVIEW_COLORS.DARK_BLUE,
          fontWeight: 'bold'
        }}>
          Overview - {fundName}
        </h3>
        <div className="line d-none d-md-block" style={{ backgroundColor: OVERVIEW_COLORS.DARK_BLUE, width: '40px', height: '3px' }}></div>
      </div>

      <div className="fund-meta" style={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: '20px',
        margin: '30px 0',
        justifyContent: 'space-between'
      }}>
        <div className="meta-item" style={{
          backgroundColor: '#8B1E2D',
          color: OVERVIEW_COLORS.WHITE,
          borderRadius: '12px',
          padding: '20px',
          textAlign: 'left',
          boxShadow: '0 4px 12px rgba(139, 30, 45, 0.2)',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          flex: '1',
          minWidth: '200px',
          maxWidth: '250px'
        }}>
          <div style={{ color: OVERVIEW_COLORS.WHITE, fontWeight: 'bold', marginBottom: '8px' }}>Fund Performance (1Yr)</div>
          <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: OVERVIEW_COLORS.WHITE }}>
            {fundDetails.performance?.oneYear ? `${fundDetails.performance.oneYear.toFixed(2)}%` : 'N/A'}
          </div>
        </div>
        <div className="meta-item" style={{
          backgroundColor: '#8B1E2D',
          color: OVERVIEW_COLORS.WHITE,
          borderRadius: '12px',
          padding: '20px',
          textAlign: 'left',
          boxShadow: '0 4px 12px rgba(139, 30, 45, 0.2)',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          flex: '1',
          minWidth: '200px',
          maxWidth: '250px'
        }}>
          <div style={{ color: OVERVIEW_COLORS.WHITE, fontWeight: 'bold', marginBottom: '8px' }}>Inception Date</div>
          <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: OVERVIEW_COLORS.WHITE }}>
            {fundDetails.start || 'N/A'}
          </div>
        </div>
        <div className="meta-item" style={{
          backgroundColor: '#8B1E2D',
          color: OVERVIEW_COLORS.WHITE,
          borderRadius: '12px',
          padding: '20px',
          textAlign: 'left',
          boxShadow: '0 4px 12px rgba(139, 30, 45, 0.2)',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          flex: '1',
          minWidth: '200px',
          maxWidth: '250px'
        }}>
          <div style={{ color: OVERVIEW_COLORS.WHITE, fontWeight: 'bold', marginBottom: '8px' }}>Management Fee</div>
          <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: OVERVIEW_COLORS.WHITE }}>
            {typeof fundDetails.Fee === 'number' ? `${fundDetails.Fee.toFixed(2)}%` : fundDetails.Fee}
          </div>
        </div>
        <div className="meta-item" style={{
          backgroundColor: '#8B1E2D',
          color: OVERVIEW_COLORS.WHITE,
          borderRadius: '12px',
          padding: '20px',
          textAlign: 'left',
          boxShadow: '0 4px 12px rgba(139, 30, 45, 0.2)',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          flex: '1',
          minWidth: '200px',
          maxWidth: '250px'
        }}>
          <div style={{ color: OVERVIEW_COLORS.WHITE, fontWeight: 'bold', marginBottom: '8px' }}>AUM</div>
          <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: OVERVIEW_COLORS.WHITE }}>
            {typeof fundDetails.aum === 'number' ? `${fundDetails.aum.toFixed(2)}%` : fundDetails.aum}
          </div>
        </div>
      </div>

      <div className="fund-description" style={{
        margin: '30px 0',
        width: '100%',
        maxWidth: 'none'
      }}>
        <div className="mb-4 d-flex align-items-center" style={{ gap: '0.5rem', justifyContent: 'flex-start' }}>
          <div className="line d-none d-md-block" style={{ backgroundColor: OVERVIEW_COLORS.DARK_BLUE, width: '40px', height: '3px' }}></div>
          <h3 style={{
            marginBottom: 0,
            color: OVERVIEW_COLORS.DARK_BLUE,
            fontWeight: 'bold',
            textAlign: 'left'
          }}>
            <span role="img" aria-label="memo" style={{ marginRight: '8px' }}>📝</span>
            Fund Description
          </h3>
        </div>
        <p style={{
          textAlign: 'left',
          fontSize: '1.1rem',
          lineHeight: '1.6',
          margin: '0',
          maxWidth: 'none',
          width: '100%',
          display: 'block'
        }}>{fundDetails.description || 'N/A'}</p>
      </div>

      <div className="investment-objective" style={{ margin: '30px 0' }}>
        <div className="mb-4 d-flex align-items-center" style={{ gap: '0.5rem', justifyContent: 'flex-start' }}>
          <div className="line d-none d-md-block" style={{ backgroundColor: OVERVIEW_COLORS.DARK_BLUE, width: '40px', height: '3px' }}></div>
          <h3 style={{
            marginBottom: 0,
            color: OVERVIEW_COLORS.DARK_BLUE,
            fontWeight: 'bold',
            textAlign: 'left'
          }}>
            <span role="img" aria-label="target" style={{ marginRight: '8px' }}>🎯</span>
            Investment Objective
          </h3>
        </div>
        <p style={{ textAlign: 'left', fontSize: '1.1rem', lineHeight: '1.6', margin: '0', maxWidth: 'none', width: '100%', display: 'block' }}>{fundDetails.investmentObjective || 'N/A'}</p>
      </div>

      <div className="key-features" style={{
        backgroundColor: OVERVIEW_COLORS.MEDIUM_BLUE,
        padding: '30px',
        borderRadius: '12px',
        margin: '30px 0',
        color: OVERVIEW_COLORS.WHITE
      }}>
        <div className="mb-4 d-flex align-items-center" style={{ gap: '0.5rem', justifyContent: 'flex-start' }}>
          <div className="line d-none d-md-block" style={{ backgroundColor: OVERVIEW_COLORS.WHITE, width: '40px', height: '3px' }}></div>
          <h3 style={{
            marginBottom: 0,
            color: OVERVIEW_COLORS.WHITE,
            fontWeight: 'bold',
            textAlign: 'left'
          }}>
            <span role="img" aria-label="key" style={{ marginRight: '8px' }}>🔑</span>
            Key Features
          </h3>
        </div>
        <ul style={{
          listStyle: 'none',
          padding: 0,
          margin: 0
        }}>
          {fundDetails.keyFeatures && fundDetails.keyFeatures.length > 0 ? (
            fundDetails.keyFeatures.map((feature, index) => (
              <li key={index} style={{
                padding: '10px 0',
                borderBottom: index < fundDetails.keyFeatures.length - 1 ? `1px solid ${OVERVIEW_COLORS.WHITE}40` : 'none',
                fontSize: '1.1rem'
              }}>
                <span style={{ marginRight: '10px', color: '#3ed68c' }}>✓</span>
                {feature}
              </li>
            ))
          ) : (
            <li style={{ padding: '10px 0', fontSize: '1.1rem' }}>
              <span style={{ marginRight: '10px', color: '#3ed68c' }}>✓</span>
              No key features available
            </li>
          )}
        </ul>
      </div>

      <div className="fund-performance" style={{
        backgroundColor: 'transparent',
        borderRadius: '12px',
        padding: '30px',
        margin: '30px 0'
      }}>
        <div className="mb-4 d-flex align-items-center" style={{ gap: '0.5rem', justifyContent: 'flex-start' }}>
          <div className="line d-none d-md-block" style={{ backgroundColor: OVERVIEW_COLORS.DARK_BLUE, width: '40px', height: '3px' }}></div>
          <h3 style={{
            marginBottom: 0,
            color: OVERVIEW_COLORS.DARK_BLUE,
            fontWeight: 'bold',
            textAlign: 'left'
          }}>
            <span role="img" aria-label="chart" style={{ marginRight: '8px' }}>📈</span>
            Performance
          </h3>
        </div>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
          gap: '15px'
        }}>
          {['oneMonth', 'threeMonth', 'sixMonth', 'oneYear', 'threeYear', 'fiveYear'].map((period, index) => {
            const labelMap = {
              oneMonth: '1 Month',
              threeMonth: '3 Months',
              sixMonth: '6 Months',
              oneYear: '1 Year',
              threeYear: '3 Years',
              fiveYear: '5 Years'
            };
            const value = fundDetails.performance?.[period];
            return (
              <div key={index} style={{
                backgroundColor: OVERVIEW_COLORS.MEDIUM_BLUE,
                color: OVERVIEW_COLORS.WHITE,
                padding: '15px',
                borderRadius: '8px',
                textAlign: 'center'
              }}>
                <div style={{ fontSize: '0.9rem', marginBottom: '5px' }}>{labelMap[period]}</div>
                <div style={{
                  fontSize: '1.2rem',
                  fontWeight: 'bold',
                  color: value >= 0 ? '#3ed68c' : '#ff6b6b'
                }}>
                  {value !== undefined && value !== null ? `${value}%` : 'N/A'}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <div className="risk-profile" style={{
        backgroundColor: '#8B1E2D',
        color: OVERVIEW_COLORS.WHITE,
        borderRadius: '12px',
        padding: '30px',
        margin: '30px 0',
        boxShadow: '0 4px 12px rgba(139, 30, 45, 0.2)'
      }}>
        <div className="mb-4 d-flex align-items-center" style={{ gap: '0.5rem', justifyContent: 'flex-start' }}>
          <div className="line d-none d-md-block" style={{ backgroundColor: OVERVIEW_COLORS.WHITE, width: '40px', height: '3px' }}></div>
          <h3 style={{
            marginBottom: 0,
            color: OVERVIEW_COLORS.WHITE,
            fontWeight: 'bold',
            textAlign: 'left'
          }}>
            <span role="img" aria-label="warning" style={{ marginRight: '8px' }}>⚠️</span>
            Risk Level
          </h3>
        </div>
        <div className="risk-meter" style={{ margin: '20px 0' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: 'rgba(255,255,255,0.2)',
            borderRadius: '25px',
            padding: '10px 20px',
            marginBottom: '10px'
          }}>
            <span style={{ fontSize: '0.9rem' }}>Low</span>
            <span style={{ fontSize: '0.9rem' }}>Medium</span>
            <span style={{ fontSize: '0.9rem' }}>High</span>
          </div>
          <div style={{
            height: '8px',
            backgroundColor: 'rgba(255,255,255,0.3)',
            borderRadius: '4px',
            position: 'relative',
            marginBottom: '15px'
          }}>
            <div style={{
              height: '100%',
              width: fundDetails.riskLevel === 'high' ? '100%' : fundDetails.riskLevel === 'medium' ? '66%' : '33%',
              backgroundColor: fundDetails.riskLevel === 'high' ? '#ff4757' : fundDetails.riskLevel === 'medium' ? '#ffa502' : '#2ed573',
              borderRadius: '4px',
              transition: 'width 0.3s ease'
            }}></div>
          </div>
        </div>
        <p style={{
          fontSize: '1.1rem',
          lineHeight: '1.6',
          margin: 0,
          textAlign: 'center'
        }}>
          This fund has a <strong style={{ color: '#3ed68c' }}>
            {fundDetails.riskLevel ? fundDetails.riskLevel.toLowerCase() : 'medium'}
          </strong> risk level due to the volatile nature of cryptocurrency markets.
        </p>
      </div>
    </div>
  );
};
export default Overview;