import React, { useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import './fund.css';
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  BarElement,
  Filler,
  CategoryScale, // for X-axis
  LinearScale,   // for Y-axis
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  LineElement,
  PointElement,
  BarElement,
  Filler,
  CategoryScale,
  LinearScale,
  Tooltip,
  Legend
);

const Performance = ({ fundName, performanceData, annualPerformanceData, lineChartOptions }) => {
  useEffect(() => {
    return () => {
      console.log('Performance component unmounting');
    };
  }, [performanceData, annualPerformanceData, lineChartOptions]);
  return (
    <div className="performance-container">
      <h3 className="fund-header">{fundName}</h3>

      <div className="charts-container ms-4">
        <div className="chart-box">
          <h3>Performance Trend</h3>
          <div style={{ position: 'relative', height: '300px' }}>
            <Line data={performanceData} options={lineChartOptions} />
          </div>
          <p className="chart-note">Quarterly returns over the past 3 years</p>
        </div>

        <div className="chart-box">
          <h3>Annual Performance Comparison</h3>
          <div style={{ position: 'relative', height: '300px' }}>
            <Line data={annualPerformanceData} options={lineChartOptions} />
          </div>
          <p className="chart-note">Benchmark returns: 10% (2021), 15% (2022), 25% (2023)</p>
        </div>
      </div>

      <div className="performance-metrics">
        <h3>Performance Metrics</h3>
        <table border="1" cellPadding="5" width={"100%"}>
          <thead>
            <tr>
              <th>Metric</th>
              <th>Fund</th>
              <th>Benchmark</th>
              <th>Difference</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Cumulative Return</td>
              <td>105.4%</td>
              <td>75.0%</td>
              <td className="positive-return">+30.4%</td>
            </tr>
            <tr>
              <td>Annualized Return</td>
              <td>27.1%</td>
              <td>20.5%</td>
              <td className="positive-return">****%</td>
            </tr>
            <tr>
              <td>Volatility (Std Dev)</td>
              <td>28.5%</td>
              <td>22.0%</td>
              <td className="negative-return">****%</td>
            </tr>
            <tr>
              <td>Sharpe Ratio</td>
              <td>0.95</td>
              <td>0.82</td>
              <td className="positive-return">+0.13</td>
            </tr>
            <tr>
              <td>Maximum Drawdown</td>
              <td>-18.2%</td>
              <td>-12.5%</td>
              <td className="negative-return">-5.7%</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Performance;