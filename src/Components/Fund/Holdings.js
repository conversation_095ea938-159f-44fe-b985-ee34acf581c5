import React, { useEffect } from 'react';
import './fund.css';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS,  ArcElement, Filler, Tooltip, Legend } from 'chart.js';

// Register required chart elements and plugins
ChartJS.register(Arc<PERSON><PERSON>, Tooltip, Filler, Legend);

const Holdings = ({ fundName, holdings, coinPieData, categoryPieData, chartOptions }) => {
  useEffect(() => {
    // Check if pie chart data exists
    return () => {
      console.log('Holdings component unmounting');
    };
  }, [fundName, holdings, coinPieData, categoryPieData]);
  return (
    <div className="holdings-container ms-4">
      <h3 className="h3">Current holdings of the&nbsp;{fundName}</h3>

      <table border="1" cellPadding="5" width={"100%"}>
        <thead>
          <tr>
            <th>Coin</th>
            <th>Category</th>
            <th>Current Price</th>
            <th>Valuation</th>
          </tr>
        </thead>
        <tbody>
          {holdings.map((item, index) => (
            <tr key={index}>
              <td>{item.coin}</td>
              <td>{item.category}</td>
              <td>${item.price.toLocaleString()}</td>
              <td>${item.value}</td>
            </tr>
          ))}
        </tbody>
      </table>

      <div className="charts-container">
        <div className="chart-box">
          <h3>Portfolio by Coin</h3>
          <div className='centered-chart' >
            <Pie data={coinPieData} options={chartOptions} />
          </div>
        </div>
        <div className="chart-box">
          <h3>Portfolio by Category</h3>
          <div className='centered-chart' >
            <Pie data={categoryPieData} options={chartOptions} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Holdings;