import React from 'react';
import { FaFileAlt } from 'react-icons/fa';
import './fund.css';

const Documents = ({ fundName }) => {

  const documents = [
    { name: "Fact sheet" },
    { name: "Product disclosure statement" },
    { name: "Annual report" },
    { name: "Half-year financial report" },
    { name: "PDS reference guide" },
    { name: "Performance summary" }
  ];

  return (
    <div className="documents-section ms-4">
      <h3 className="h3">{fundName} Documents</h3>
      <div className="document-grid">
        {documents.map((doc, i) => (
          <div key={i} className="document-card">
            <div className="document-title">
              <FaFileAlt className="document-icon" aria-label="Document icon" />
              <h3>{doc.name}</h3>
            </div>
            <button className="download-button">
              Download <span style={{ fontSize: '1rem' }}>⬇️</span>
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};
export default Documents;