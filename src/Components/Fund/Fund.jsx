import React from 'react';
import './fund.css';
import Tabs from '../../App/Tabs/Tabs.jsx';
import Tab from '../../App/Tabs/Tab.jsx';
import Overview from './Overview.js';
import Holdings from './Holdings.js';
import Performance from './Performance.js';
import Documents from './Documents.js';
import WhatIf from './WhatIf.js';

/*
const formatCurrency = (value) =>
  typeof value === 'number' ? `$${(value / 1_000_000).toFixed(1)}M` : 'N/A';
*/
const Fund = ({
  fundName,
  holdings = [],
  fundDetails = {},
  performanceData = null,
  annualPerformanceData = [],
  lineChartOptions = {},
  isMobile = window.innerWidth <= 768
}) => {
  return (
    <div className="fund-container fund-main-container" style={{ marginTop: window.innerWidth <= 768 ? '80px' : '20px' }}>
      {/* Fund header is now completely removed */}
      
      <div className="fund-content">
        {/* Add a top margin to ensure tabs are at the top */}
        <div className="tabs-wrapper" style={{ marginTop: window.innerWidth <= 768 ? '10px' : '20px' }}>
          <Tabs defaultTab="Overview">
            {/* Overview Tab */}
            <Tab label="Overview" className="tab-content-centered">
              <Overview fundDetails={fundDetails} fundName={fundName} />
            </Tab>

            {/* Holdings Tab */}
            <Tab label="Holdings" className="tab-content-centered">
              <Holdings
                categoryPieData={{
                  labels: [...new Set(holdings.map(h => h.category))],
                  datasets: [{
                    data: Object.values(holdings.reduce((acc, h) => {
                      acc[h.category] = (acc[h.category] || 0) + h.allocation;
                      return acc;
                    }, {})),
                    backgroundColor: [
                      '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'
                    ]
                  }]
                }}
                chartOptions={{
                  responsive: true,
                  plugins: {
                    legend: { position: 'right' }
                  }
                }}
                coinPieData={{
                  labels: holdings.map(h => h.name),
                  datasets: [{
                    data: holdings.map(h => h.allocation),
                    backgroundColor: [
                      '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                      '#FF9F40', '#8AC249', '#EA526F', '#25CED1', '#FCEADE'
                    ]
                  }]
                }}
                fundName={fundName}
                holdings={holdings}
              />
            </Tab>

            {/* Performance Tab */}
            <Tab label="Performance" className="tab-content-centered">
              <Performance
                annualPerformanceData={annualPerformanceData}
                fundName={fundName}
                lineChartOptions={lineChartOptions}
                performanceData={performanceData}
              />
            </Tab>

            {/* Documents Tab */}
            <Tab label="Documents" className="tab-content-centered">
              <Documents fundName={fundName} />
            </Tab>

            {/* What-If Tab */}
            <Tab label="What-If" className="tab-content-centered">
              <WhatIf
                fundName={fundName}
                lineChartOptions={lineChartOptions}
                performanceData={performanceData}
              />
            </Tab>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Fund;
