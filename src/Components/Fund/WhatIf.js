import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import './fund.css';

const WhatIf = ({ fundName, performanceData, lineChartOptions }) => {
  // State for investment amount with default of $10,000
  const [investmentAmount, setInvestmentAmount] = useState(10000);
  // State for formatted display of the investment amount
  const [formattedAmount, setFormattedAmount] = useState('$10,000');
  // State for cumulative returns
  const [cumulativeReturns, setCumulativeReturns] = useState([]);
  // State for chart data
  const [whatIfData, setWhatIfData] = useState(null);

  // Calculate returns when investment amount changes
  useEffect(() => {

    // Check if performance data exists and has the expected structure
    if (!performanceData || !performanceData.datasets || !performanceData.datasets[0] || !performanceData.datasets[0].data) {
      console.error('Performance data is missing or has invalid structure:', performanceData);
      return;
    }

    // Calculate cumulative returns based on monthly performance data
    const newCumulativeReturns = performanceData.datasets[0].data.reduce((acc, monthlyReturn, index) => {
      // Convert the monthly percentage to a decimal (e.g., 5% becomes 0.05)
      const monthlyReturnDecimal = monthlyReturn / 100;

      if (index === 0) {
        // First month: initial investment + first month return
        return [...acc, investmentAmount * (1 + monthlyReturnDecimal)];
      } else {
        // Subsequent months: previous value + current month return
        const previousValue = acc[index - 1];
        return [...acc, previousValue * (1 + monthlyReturnDecimal)];
      }
    }, []);

    setCumulativeReturns(newCumulativeReturns);

    // Create data for the what-if chart
    const newWhatIfData = {
      labels: performanceData.labels,
      datasets: [
        {
          label: `$${investmentAmount.toLocaleString()} Investment Growth`,
          data: newCumulativeReturns,
          borderColor: '#4BC0C0',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderWidth: 2,
          pointRadius: 3,
          tension: 0.3,
          fill: true
        }
      ]
    };

    setWhatIfData(newWhatIfData);

    // Format the investment amount for display
    setFormattedAmount('$' + investmentAmount.toLocaleString());
  }, [investmentAmount, performanceData]);

  // Handle slider change
  const handleSliderChange = (e) => {
    setInvestmentAmount(Number(e.target.value));
  };

  // Custom options for the what-if chart
  const whatIfOptions = {
    ...lineChartOptions,
    scales: {
      ...lineChartOptions.scales,
      y: {
        ...lineChartOptions.scales.y,
        ticks: {
          callback: function(value) {
            return '$' + value.toLocaleString();
          }
        },
        title: {
          display: true,
          text: 'Value ($)'
        }
      }
    },
    plugins: {
      ...lineChartOptions.plugins,
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.dataset.label + ': $' + context.parsed.y.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
          }
        }
      }
    }
  };

  // Calculate final value and total return
  const finalValue = cumulativeReturns.length > 0 ? cumulativeReturns[cumulativeReturns.length - 1] : 0;
  const totalReturn = investmentAmount > 0 ? ((finalValue - investmentAmount) / investmentAmount) * 100 : 0;
  const totalGain = finalValue - investmentAmount;

  return (
    <div className="whatif-container ms-4">
      <h3 className="fund-header">{fundName} performance simulation</h3>

      <div className="slider-container">
        <label style={{textAlign: "left"}} htmlFor="investment-slider">Investment Amount: {investmentAmount.toLocaleString()}</label>
        <input
          type="range"
          id="investment-slider"
          min="10000"
          max="1000000"
          step="10000"
          value={investmentAmount}
          onChange={handleSliderChange}
          className="investment-slider"
        />
      </div>
      <div className="whatif-content">
        <div className="investment-summary">
          <div className="summary-box">
            <h3>Initial Investment</h3>
            <p className="summary-value">${investmentAmount.toLocaleString()}</p>
          </div>
          <div className="summary-box">
            <h3>Current Value</h3>
            <p className={`summary-value ${finalValue >= investmentAmount ? 'positive-return' : 'negative-return'}`}>
              ${finalValue.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              })}
            </p>
          </div>
          <div className="summary-box">
            <h3>Total Gain</h3>
            <p className={`summary-value ${totalGain >= 0 ? 'positive-return' : 'negative-return'}`}>
              ${totalGain.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              })}
            </p>
          </div>
          <div className="summary-box">
            <h3>Total Return</h3>
            <p className={`summary-value ${totalReturn >= 0 ? 'positive-return' : 'negative-return'}`}>
              {totalReturn.toFixed(2)}%
            </p>
          </div>
        </div>
        <div className="chart-box">
          <h3>Investment Growth Over Time</h3>
          <div style={{ position: 'relative', height: '300px' }}>
            {whatIfData && <Line data={whatIfData} options={whatIfOptions} />}
          </div>
          <p className="chart-note">Monthly growth of a {formattedAmount} investment over the past 3 years</p>
        </div>
      </div>
    </div>
  );
};

export default WhatIf;
