import { useParams } from 'react-router-dom';
import FundPage from '../Fund/FundPage.jsx';

const FundsDetail = () => {
  const { fundId } = useParams();  // Get the fundId, which will include 'funds-'

  // Simulated fund data (for the purpose of this example)
  const fundData = [
    { id: 'capital-fund', component: <FundPage fund="capital-fund" /> },
    { id: 'momentum-fund', component: <FundPage fund="momentum-fund" /> },
    { id: 'defi-fund', component: <FundPage fund="defi-fund" /> },
    { id: 'income-fund', component: <FundPage fund="income-fund" /> },
    { id: 'smartbeta-fund', component: <FundPage fund="smartbeta-fund" /> },
  ];

  // Find the fund that matches the fundId
  const fund = fundData.find(fund => fund.id === fundId);

  // If no fund matches, show a "not found" message
  if (!fund) {
    return <div className='text-black py-5 my-5 display-4 fw-bold'>Fund not found</div>;
  }

  return (
    <>
    {fund.component}
    </>
  );
}

export default FundsDetail;
