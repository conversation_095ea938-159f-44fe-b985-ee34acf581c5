/* Variables */
:root {
  --font-header: "Libre Baskerville", serif;
  --font-subheader: "Lato", sans-serif;
  --font-body: "Lato", sans-serif;
  --font-code: "Instrument Serif", monospace;
  --font-brand: "Lexend", sans-serif;
  --body-content-px-margin-dekstop: 30px;
  --body-content-px-margin-mobile: 10px;
  --body-content-py-margin-dekstop: 105px;
  --body-content-py-margin-mobile: 25px;
  --cta-section-back-color: #f9f9f9;
}

/* Line Animation */
.line {
  display: inline-block;
  height: 2px;
  background-color: #7e0e0e;
  width: 40px;
  transition: width 0.4s ease;
}
.help-text:hover .line {
  width: 100px;
}

/* CTA Section */
.cta-section {
  background-color: var(--cta-section-back-color) !important;
  padding: 0.75rem !important;
  margin-bottom: 0 !important;
  min-height: 80px; /* Ensure minimum height for proper centering */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
.enhanced-cta {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
  transition: all 0.5s ease;
  color: #333;
  padding: 2.5rem 1.5rem !important;
}

/* CTA Button Enhancement */
.cta-button {
  transition: transform 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease;
  font-weight: 500;
  background-color: rgba(13, 110, 253, 0.85); /* Same blue but with transparency */
  color: white;
  border: none;
  padding: 6px 16px !important; /* Reduced padding */
  box-shadow: 0 4px 12px rgba(13, 110, 253, 0.2); /* Reduced shadow */
  letter-spacing: 0.5px;
  font-size: 0.9rem; /* Smaller font */
}
.cta-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 18px rgba(13, 110, 253, 0.4);
  background-color: rgba(11, 94, 215, 0.8); /* Full opacity on hover */
  opacity: 0.8;
}

/* Spacing Helpers */
.body-content-py-margin {
  margin: var(--body-content-py-margin-dekstop) 0 !important;
}
.body-content-px-margin {
  padding: 0 var(--body-content-px-margin-dekstop) !important;
}

/* Typography - Headings */
.tagline, .display-1, .display-2, .display-3, .display-4, .display-5, .display-6,
h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: var(--font-header);
  font-weight: 400;
  line-height: 1.2;
}
.display-1 { font-size: 5rem; }
.display-2 { font-size: 4.5rem; }
.display-3 { font-size: 4rem; }
.display-4 { font-size: 2.8rem; letter-spacing: 2px; }
.display-5 { font-size: 3rem; }
.display-6, .tagline { font-size: 2.5rem; }

.h1 { font-size: 56px; font-weight: 500; line-height: 1.0; }
.h2, .heading-lg { font-size: 33px; font-weight: 500; line-height: 1.3; }
.h21, .heading-lg2 { font-size: 24px; font-weight: 500; line-height: 1.3; }
.h2c, .heading-lg3 {
  font-size: 24px;
  font-weight: 500;
  text-align: left;
  margin-left: 1.5rem;
}
.h3, .heading-md { font-size: 23px; font-weight: 500; }
.h4, .heading-sm { font-size: 16px; font-weight: 500; }

/* Typography - Body Text */
.body-text,
.body-text-small,
.body-text-large,
.body-text-center,
p {
  font-family: var(--font-body);
  font-size: 18px;
  font-weight: 400;
  line-height: 1.6;
  text-align: left;
}
.body-text-small {
  font-size: 14px;
  line-height: 1.6;
}
.body-text-large {
  font-size: 16px;
  line-height: 1.6;
  text-align: left;
}
.body-text-center {
  font-size: 18px;
  font-weight: 400;
  line-height: 1.6;
  text-align: center;
}
.caption {
  font-size: 13.6px;
  color: #555;
}
.smooth-center {
  text-align: center;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* Buttons */
.btn {
  font-family: var(--font-body) !important;
  font-weight: 600;
}
.sec-4-button {
  font-size: 14px !important;
  font-weight: 600 !important;
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
  min-width: 200px !important;
  display: inline-block !important;
  background-color: var(--primary, #8B1E2D);
  color: white;
  border: none;
  border-radius: 20px !important; /* Added rounded corners */
  transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}
.sec-4-button:hover {
  background-color: #0d6efd !important;
  color: white !important;
  border-color: #0d6efd !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border-radius: 20px !important; /* Added rounded corners */
}
.sec-4-button:focus {
  box-shadow: none !important;
  outline: none;
}
.contact-button-basic {
  border: 2px solid white;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Fonts */
.brand-name {
  font-family: var(--font-brand);
  font-weight: 400;
  color: #111;
}

/* Specific override for About page heading */
.display-4.no-break-title {
  font-size: 2.2rem !important;
  letter-spacing: 1px !important;
}

.display-4.no-break-title .brand-name {
  font-weight: 400 !important;
  font-size: inherit !important;
}

/* Form elements */
input, select, textarea, .form-control {
  font-family: var(--font-body);
}

/* Code elements */
pre, code, kbd, samp {
  font-family: var(--font-code);
}

/* Animations */
.fade-in-stagger {
  opacity: 0;
  transform: translateY(10px);
  animation: fadeInUp 0.6s ease forwards;
}
.fade-in-stagger.delay-1 { animation-delay: 0.3s; }
.fade-in-stagger.delay-2 { animation-delay: 0.6s; }

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(10px); }
  to   { opacity: 1; transform: translateY(0); }
}
@keyframes pulse {
  0%   { transform: scale(1); }
  50%  { transform: scale(1.3); }
  100% { transform: scale(1.2); }
}
@keyframes spin {
  from { transform: rotate(0deg); }
  to   { transform: rotate(360deg); }
}

/* Footer Hover Effects */
.footer-main a {
  transition: color 0.3s ease, transform 0.3s ease;
}
.footer-main a:hover {
  color: var(--primary);
  transform: translateY(-2px);
}
.footer-main .btn:hover {
  background-color: white;
  color: black;
  border-color: white;
  transform: scale(1.05);
  transition: all 0.3s ease;
}

/* Underline effect for nav links */
.footer-link-animate {
  position: relative;
  display: inline-block;
  transition: color 0.3s ease, transform 0.3s ease;
}
.footer-link-animate::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -4px;
  height: 2px;
  width: 0;
  background-color: var(--primary, white);
  transition: width 0.3s ease;
}
.footer-link-animate:hover::after {
  width: 100%;
}
.footer-link-animate:hover {
  color: var(--primary, #00e6c3);
  transform: translateY(-2px);
}
.footer-link-animate:hover i {
  animation: pulse 0.6s ease-out;
  color: var(--primary, #00e6c3);
  transform: scale(1.2);
}

/* Responsive */
@media (max-width: 768px) {
  .smooth-center { padding: 0 1rem; }
  .body-content-py-margin {
    margin: var(--body-content-py-margin-mobile) 0 !important;
  }
  .body-content-px-margin {
    padding: 0 var(--body-content-px-margin-mobile) !important;
  }
  .h2 { font-size: 22px !important; }
  .h1 { font-size: 40px !important; }
  .body-text { font-size: 16px; }
  .body-text-large { font-size: 14px !important; }
  .body-text-small { font-size: 12px; }

  /* Center images on mobile */
  .text-center-mbl {
    text-align: center !important;
    display: flex;
    justify-content: center !important;
    align-items: center !important;
  }

  .text-center-mbl img {
    margin: 0 auto !important;
  }

  /* Center buttons on mobile */
  a.btn, button.btn, .btn {
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

  /* Specifically for sec-4-button which is used for Explore buttons */
  .sec-4-button {
    text-align: center !important;
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    width: auto !important;
  }

  /* Container for buttons */
  .col-lg-7 .btn, .col-md-7 .btn, .col-sm-12 .btn {
    margin-left: auto !important;
    margin-right: auto !important;
    display: block !important;
  }
}

.social-media-icons i {
  color: #0d6efd;
  transition: color 0.3s ease, transform 0.3s ease;
}

/* Pulse on hover (still optional) */
.footer-link-animate:hover i {
  animation: pulse 0.6s ease-out;
  color: #0b5ed7;
  transform: scale(1.2);
}

.contact-button-basic:hover {
  transform: scale(1.05);
}


.footer-main .btn:not(.contact-button-blue):hover {
  background-color: white;
  color: black;
}

.bullet-icon {
  transition: transform 0.2s ease-in-out;
}

.bullet-svg {
  transition: transform 0.2s ease, fill 0.2s ease;
}

li:hover .bullet-icon {
  transform: scale(1.3);
}

li:hover .bullet-svg {
  fill: var(--primary, #007BFF); /* fallback to bootstrap primary blue */
}

.signup-offset {
  padding-top: calc(80px + 1rem); /* Reduced from 90px + 1.5rem */
}

.scroll-offset-section {
  scroll-margin-top: 80px; /* Reduced from 90px */
}

/* Add specific styling for section headings to ensure they're visible */
section h2.h2, 
section h2.mobile-header {
  padding-top: 5px; /* Reduced from 10px */
  margin-top: 5px; /* Reduced from 10px */
}

/* Ensure all sections with IDs have proper scroll margin */
[id] {
  scroll-margin-top: 80px; /* Reduced from 90px */
}

/* Footer Contact Button - Custom styling with high specificity */
.footer-main .footer-contact-button {
  background-color: var(--primary) !important; /* Dark red color from footer */
  color: white !important;
  border: 2px solid white !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  top: 0 !important;
}

.footer-main .footer-contact-button:hover {
  background-color: #0d6efd !important; /* Blue color matching social media icons */
  color: white !important;
  transform: translateY(-5px) scale(1.05) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  border-color: white !important;
}

/* Override any Bootstrap btn hover styles */
.footer-main .btn:hover {
  background-color: #0d6efd !important;
  color: white !important;
  border-color: white !important;
}

.history-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 30px;
}

.arrow-icon {
  font-size: 2rem;
  color: #7e0e0e;
  animation: pulseDown 1.2s infinite;
}

@keyframes pulseDown {
  0%   { transform: translateY(0); opacity: 1; }
  50%  { transform: translateY(5px); opacity: 0.6; }
  100% { transform: translateY(0); opacity: 1; }
}

.history-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
}

.arrow-svg {
  animation: pulseDown 1.2s infinite;
}

@keyframes pulseDown {
  0%   { transform: translateY(0); opacity: 1; }
  50%  { transform: translateY(5px); opacity: 0.6; }
  100% { transform: translateY(0); opacity: 1; }
}

.animate-fade-in {
  opacity: 0;
  transform: translateY(-10px);
}
.fade-in-section.is-visible .animate-fade-in {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive typography adjustments */
@media (max-width: 768px) {
  /* Adjust heading sizes for mobile */
  .tagline, .display-1, .display-2, .display-3, .display-4, .display-5, .display-6,
  h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.3;
    margin-bottom: 0.5em;
  }
  
  /* Specific size adjustments */
  .display-1 { font-size: 3rem; }
  .display-2 { font-size: 2.8rem; }
  .display-3 { font-size: 2.6rem; }
  .display-4 {
    font-size: 2.4rem;
    letter-spacing: 2px; /* Reduced from 4px */
  }

  /* Override for About page heading on mobile */
  .display-4.no-break-title {
    font-size: 2rem !important;
    letter-spacing: 1px !important;
  }
  .display-5 { font-size: 2.2rem; }
  .display-6, .tagline { font-size: 2rem; }
  
  .h1 { 
    font-size: 2.2rem !important; 
    line-height: 1.2 !important;
    letter-spacing: 0 !important; /* Remove letter spacing on mobile */
  }
  
  .h2, .heading-lg { 
    font-size: 1.8rem !important; 
    line-height: 1.3 !important;
  }
  
  /* Add padding to prevent text from touching screen edges */
  .container h1, .container .h1,
  .container h2, .container .h2,
  .container h3, .container .h3,
  .container h4, .container .h4,
  .container h5, .container .h5,
  .container h6, .container .h6,
  .container .display-1,
  .container .display-2,
  .container .display-3,
  .container .display-4,
  .container .display-5,
  .container .display-6 {
    padding-left: 5px;
    padding-right: 5px;
  }
  
  /* Ensure text doesn't overflow its container */
  .text-center-mbl h1, .text-center-mbl .h1,
  .text-center-mbl h2, .text-center-mbl .h2,
  .text-center-mbl h3, .text-center-mbl .h3,
  .text-center-mbl h4, .text-center-mbl .h4,
  .text-center-mbl h5, .text-center-mbl .h5,
  .text-center-mbl h6, .text-center-mbl .h6 {
    width: 100%;
    max-width: 100%;
  }
}

