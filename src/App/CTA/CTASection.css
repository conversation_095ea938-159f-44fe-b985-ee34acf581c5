.cta-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50%;
  max-width: 600px;
  margin: 0 auto;
  overflow: hidden; /* Contain content within boundaries */
}

.cta-table {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  width: 100% !important;
  margin: 0 auto;
  border: 1px solid rgba(68, 01, 07, 0.25);
  background-color: rgba(171, 176, 182, 0.05);
  box-shadow: 0 8px 24px rgba(104, 1, 7, 0.25);
  --bs-table-border-color: rgba(68, 01, 07, 0.25);
  border-color: rgba(68, 01, 07, 0.25);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  border-radius: 0.375rem;
  overflow: hidden; /* Contain content within boundaries */
}

/* Removed hover effect from CTA frame/container */

.cta-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.cta-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 0.5rem 0;
}

/* Improve text handling in CTA component */
.cta-content h3 {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: break-word !important;
  hyphens: auto !important;
  max-width: 100% !important;
  font-size: 1.25rem !important;
  line-height: 1.4 !important;
  padding: 0 5px !important;
  margin-bottom: 1rem !important;
  text-overflow: ellipsis !important;
  display: block !important;
}

/* Blue section CTA styling - for medium blue backgrounds */
.cta-blue-section .cta-table {
  border: none !important;
  background-color: #0D1B2E !important;
  --bs-table-border-color: transparent !important;
  border-color: transparent !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
}

.cta-blue-section .cta-content h3 {
  color: white !important;
}

.cta-blue-section .cta-button {
  background-color: rgba(13, 110, 253, 0.85) !important;
  color: white !important;
  border: none !important;
}

/* Dark blue section CTA styling - for darkest blue backgrounds */
.cta-dark-section .cta-table {
  border: none !important;
  background-color: #0F2037 !important;
  --bs-table-border-color: transparent !important;
  border-color: transparent !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
}

.cta-dark-section .cta-content h3 {
  color: white !important;
}

.cta-dark-section .cta-button {
  background-color: rgba(13, 110, 253, 0.85) !important;
  color: white !important;
  border: none !important;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .cta-container {
    width: 70%;
  }
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  .cta-container {
    width: 95% !important;
    padding: 0 !important;
    margin: 0 auto !important;
    max-width: 100% !important;
  }

  .cta-table {
    width: 100% !important;
    margin: 0 auto !important;
    padding: 1rem 0.5rem !important;
  }

  .cta-content {
    text-align: center !important;
    width: 100% !important;
    padding: 0 !important;
  }
  
  /* Improve text handling on mobile */
  .cta-content h3 {
    font-size: 0.9rem !important;
    line-height: 1.3 !important;
    margin-bottom: 0.75rem !important;
    padding: 0 5px !important;
    word-break: break-word !important;
  }

  .cta-button-container {
    width: 100% !important;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .cta-button {
    width: auto !important;
    margin: 0 auto !important;
    font-size: 0.9rem !important;
    padding: 0.4rem 1rem !important;
    white-space: normal !important;
    text-align: center !important;
  }
  
  /* Allow button text to wrap if needed */
  .cta-button span, 
  .cta-button svg {
    flex-shrink: 0;
  }
}

/* Extra small devices */
@media (max-width: 480px) {
  .cta-content h3 {
    font-size: 0.85rem !important;
    line-height: 1.2 !important;
    padding: 0 2px !important;
  }
  
  .cta-button {
    font-size: 0.85rem !important;
    padding: 0.35rem 0.9rem !important;
  }
  
  .cta-table {
    padding: 0.75rem 0.25rem !important;
  }
}

/* Button styling improvements */
.cta-button {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  font-weight: 500;
  background-color: rgba(13, 110, 253, 0.85);
  color: white;
  border: none;
  padding: 6px 16px !important;
  box-shadow: 0 4px 12px rgba(13, 110, 253, 0.2);
  letter-spacing: 0.5px;
  font-size: 0.9rem;
  width: auto !important; /* Ensure button width fits content */
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
}

.cta-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 0.5rem 0;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  .cta-button {
    width: auto !important; /* Ensure button width fits content */
    max-width: fit-content !important;
    margin: 0 auto !important;
    font-size: 0.9rem !important;
    padding: 0.4rem 1rem !important;
    white-space: normal !important;
    text-align: center !important;
    display: inline-flex !important;
  }
  
  .cta-button-container {
    width: 100% !important;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  /* Allow button text to wrap if needed */
  .cta-button span, 
  .cta-button svg {
    flex-shrink: 0;
  }
}

/* Extra small devices */
@media (max-width: 480px) {
  .cta-button {
    font-size: 0.85rem !important;
    padding: 0.35rem 0.9rem !important;
    width: auto !important;
    max-width: fit-content !important;
  }
}

/* Ensure the CTA button hover effect remains independent */
.cta-table:hover .cta-button {
  /* This ensures the button's hover state isn't affected by the table hover */
  transform: none;
  box-shadow: none;
}

/* Preserve the original button hover effect */
.cta-button:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 6px 18px rgba(13, 110, 253, 0.4) !important;
}
