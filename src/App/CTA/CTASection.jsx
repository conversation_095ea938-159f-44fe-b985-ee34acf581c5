import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import useFadeInOnScroll from '../../hooks/useFadeInOnScroll';
import './CTASection.css';

const CTASection = ({ title, cta, link, theme = "learn", className = "", buttonStyle = {}, titleStyle = {}, containerStyle = {} }) => {
  const [ref, isVisible] = useFadeInOnScroll();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Add window resize listener
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };
    
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Add anchor to signup links
  const getLink = () => {
    if (theme === 'signup' || link === '/signup') {
      return '/signup#account-signup';
    }
    return link;
  };

  // Add scroll to top functionality
  const handleClick = () => {
    window.scrollTo(0, 0);
  };

  // Get theme-specific icon
  const getThemeIcon = () => {
    if (theme === 'signup') {
      return <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-person-plus" viewBox="0 0 16 16">
        <path d="M6 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm4 8c0 1-1 1-1 1H1s-1 0-1-1 1-4 6-4 6 3 6 4zm-1-.004c-.001-.246-.154-.986-.832-1.664C9.516 10.68 8.289 10 6 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10z"/>
        <path fillRule="evenodd" d="M13.5 5a.5.5 0 0 1 .5.5V7h1.5a.5.5 0 0 1 0 1H14v1.5a.5.5 0 0 1-1 0V8h-1.5a.5.5 0 0 1 0-1H13V5.5a.5.5 0 0 1 .5-.5z"/>
      </svg>;
    }
    return <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-book" viewBox="0 0 16 16">
      <path d="M1 2.828c.885-.37 2.154-.769 3.388-.893 1.33-.134 2.458.063 3.112.752v9.746c-.935-.53-2.12-.603-3.213-.493-1.18.12-2.37.461-3.287.811V2.828zm7.5-.141c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388.893v9.923c-.918-.35-2.107-.692-3.287-.81-1.094-.111-2.278-.039-3.213.492V2.687zM8 1.783C7.015.936 5.587.81 4.287.94c-1.514.153-3.042.672-3.994 1.105A.5.5 0 0 0 0 2.5v11a.5.5 0 0 0 .707.455c.882-.4 2.303-.881 3.68-1.02 1.409-.142 2.59.087 3.223.877a.5.5 0 0 0 .78 0c.633-.79 1.814-1.019 3.222-.877 1.378.139 2.8.62 3.681 1.02A.5.5 0 0 0 16 13.5v-11a.5.5 0 0 0-.293-.455c-.952-.433-2.48-.952-3.994-1.105C10.413.809 8.985.936 8 1.783z"/>
    </svg>;
  };

  // Format title based on screen size
  const formatTitle = () => {
    const defaultTitle = "Learn More About Our Offerings";
    if (!title) return defaultTitle;
    
    // For mobile devices, use a shorter version if title is long
    if (windowWidth <= 768 && title.length > 40) {
      if (title.includes("Financial")) {
        return "Take the First Step";
      }
      return title.split(' ').slice(0, 6).join(' ') + '...';
    }
    
    return title;
  };

  return (
    <div
      ref={ref}
      className={`fade-in-section mx-auto cta-container ${isVisible ? 'is-visible' : ''} ${className}`}
    >
      <div className="table table-bordered cta-table" style={containerStyle}>
        <div className="cta-content text-center">
          <h3 className="h4 mb-3" style={titleStyle}>{formatTitle()}</h3>
          <div className="cta-button-container">
            <Link
              className="btn btn-light cta-button rounded-pill d-inline-flex align-items-center gap-2"
              to={getLink()}
              onClick={handleClick}
              style={{
                backgroundColor: 'rgba(13, 110, 253, 0.85)',
                color: 'white',
                width: 'auto',
                display: 'inline-flex',
                ...buttonStyle
              }}
            >
              {getThemeIcon()}
              <span>{cta || "Click Here"}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="10" height="24" viewBox="0 0 24 24">
                <path fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 12H5m7 7l7-7l-7-7" />
              </svg>
            </Link>
          </div>
          {/* <div className="mt-3">
            <a href='/contact'>Or get in touch</a>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default CTASection;
