import React from 'react';
import { Link } from 'react-router-dom';
import { useContent } from '../../context/ContentContext';

const FooterNew = (props) => {
  const { content } = useContent();
  const { footerNav } = content.navigation;

  const scrollToTop = () => {
    window.scrollTo(0, 0);
  };

  return (
    <div className="footer-main" data-testid="footer" style={{ backgroundColor: 'var(--primary, #8B1E2D)' }} {...props}>
      <div className="container">
        <div className="ms-3" style={{ padding: '20px', borderTop: '1px solid var(--primary)' }} />
        <div className="row">
          {/* Company Info */}
          <div className="col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-3 col-xxl-3 footer-small-screen mb-5 mb-lg-0 d-flex justify-content-center">
            <div>
              <Link className="h1 fs-3 text-white text-decoration-none footer-link-animate" to="/">
                {footerNav.company.name}
              </Link>
              <p className="body-text-small text-white mt-4">{footerNav.company.description}</p>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="col-xs-12 col-sm-12 d-flex justify-content-center col-md-6 col-lg-6 col-xl-3 col-xxl-3 footer-small-screen mb-5 mb-lg-0">
            <div>
              <div className="nav-link gap-3 mt-4 px-0 d-flex flex-column gap-2 footer-list">
                {footerNav.links.map((link) => (
                  <Link
                    className="text-white text-decoration-none footer-link-animate"
                    key={link.path}
                    onClick={scrollToTop}
                    to={link.path.includes('/signup') ? '/signup#account-signup' : link.path}
                  >
                    {link.name}
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/* Social Media Icons - blue by default */}
          <div className="col-xs-12 d-flex justify-content-center col-sm-12 col-md-6 col-lg-6 col-xl-3 col-xxl-3 footer-small-screen mb-5 mb-lg-0">
            <div>
              <div className="h4 smooth-center text-white">Follow us on</div>
              <div className="fs-6">
                <nav className="social-media-icons d-block gap-2 mt-4">
                  <div className="d-flex gap-2 mb-2">
                    {footerNav.social.map((social) => (
                      <a
                        className="footer-link-animate text-decoration-none"
                        href={social.url}
                        key={social.url}
                        rel="noreferrer"
                        target="_blank"
                      >
                        <div>
                          <i className={`bi ${social.icon}`} />
                        </div>
                      </a>
                    ))}
                  </div>
                </nav>
              </div>
            </div>
          </div>

          {/* Contact Button */}
          <div className="col-xs-12 d-flex justify-content-center col-sm-12 col-md-6 col-lg-6 col-xl-3 col-xxl-3 footer-small-screen mb-5 mb-lg-0">
            <div>
              <Link
                className="btn footer-contact-button px-5 py-3 rounded-pill"
                onClick={scrollToTop}
                to="/contact"
                style={{ 
                  backgroundColor: 'var(--primary)', 
                  color: 'white', 
                  border: '2px solid white' 
                }}
              >
                Contact
              </Link>
            </div>
          </div>

          {/* Copyright */}
          <div className="col-12 my-4">
            <div style={{ margin: '20px', borderTop: '1px solid grey' }} />
            <span className="body-text-small ms-3 text-white">{footerNav.company.copyright}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FooterNew;
